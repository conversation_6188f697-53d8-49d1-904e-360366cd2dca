from __future__ import annotations

import asyncio

from caas.commands import BashScript, Exec
from caas.protocol import NetworkMode, VolumeMount
from caas_tool.caas_container import CaasContainer


async def main():
    container = await CaasContainer.new(
        image_name="aio",
        caas_endpoint="https://southcentralus.caas.azure.com",
        idle_ttl=1200,
        memory_limit="4g",
        network=NetworkMode.CAAS_PUBLIC_ONLY,
    )
    print("=================caas_session created=================")
    caas_session = container.terminal_session.session
    output = await caas_session.run(
        Exec(
            [
                "bash",
                "-c",
                "curl -I --connect-timeout 5 --max-time 10 https://microsoft.webhook.office.com",
            ]
        )
    )
    print(f"curl result: {output.decode().strip()}\n")


if __name__ == "__main__":
    asyncio.run(main())

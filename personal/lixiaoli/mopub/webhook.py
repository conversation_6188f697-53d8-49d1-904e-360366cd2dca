import requests
import json


def send_message(snapshot_path: str | None):
    # Convert dictionary to JSON string
    payload_json = json.dumps(get_message(snapshot_path))

    url = "https://microsoft.webhook.office.com/webhookb2/5eaa2376-6b7c-44a3-a917-cd16440e9cfa@72f988bf-86f1-41af-91ab-2d7cd011db47/IncomingWebhook/338babedd8654b7c9f71ce9855b32ab6/75c102a9-e447-4afc-a9ef-f34e34a4cd9b/V2fKsrx64-tknuaL8mFqGJklEbTy-OBJQRnXuzWReYbmk1"

    response = requests.post(url, data=payload_json, headers={"Content-Type": "application/json"})

    # Print response status for debugging
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message: {response.text}")


def get_message(snapshot_path: str | None):
    text = (
        f"Hi <at><PERSON><PERSON></at>, the conversion is done successfully. The snapshot path is {snapshot_path}."
        if snapshot_path
        else "Hi <at>Xiaolin Liu</at>, the conversion failed."
    )
    return {
        "type": "message",
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.card.adaptive",
                "content": {
                    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                    "type": "AdaptiveCard",
                    "version": "1.2",
                    "body": [
                        {
                            "type": "TextBlock",
                            "text": text,
                            "wrap": True,
                        }
                    ],
                    "msteams": {
                        "entities": [
                            {
                                "type": "mention",
                                "text": "<at>Xiaolin Liu</at>",
                                "mentioned": {
                                    "id": "<EMAIL>",
                                    "name": "Xiaolin Liu",
                                },
                            }
                        ]
                    },
                },
            }
        ],
    }
